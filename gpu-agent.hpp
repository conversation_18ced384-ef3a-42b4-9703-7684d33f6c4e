// System headers
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <signal.h>
#include <syslog.h>
#include <unistd.h>

// C++ standard library headers
#include <cstdint>
#include <cstring>
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <string>

// System-specific headers
#include <sys/mman.h>
#include <sys/poll.h>
#include <sys/stat.h>

// X11 headers
#include <X11/Xlib.h>
#include <X11/Xutil.h>

// DRM headers
#include <xf86drm.h>
#include <xf86drmMode.h>

class GpuAgentException : public std::runtime_error {
public:
    explicit GpuAgentException(const std::string& message) : std::runtime_error(message) {}
};

class GpuAgent
{
public:
    GpuAgent(uint16_t width, uint16_t height, uint8_t fps = 20);
    ~GpuAgent();
    void set_capture_resolution();
    void set_drm();
    void init_drm_event();
    void start_capture();
    void free_resource();
    static void handle_page_flip(int fd, uint32_t sequence, uint32_t tv_sec, uint32_t tv_usec, void *user_data);

private:
    uint16_t width;
    uint16_t height;
    uint8_t fps = 15;
    uint32_t frame_delay_us = 100000;
    Display *display = nullptr;
    Window root_win;
    int drm_fd = 0;
    drmModeRes *res = nullptr;
    drmModeConnector *conn = nullptr;
    void *fb_data = nullptr;
    uint32_t fb_id = 0;
    uint32_t crtc_id = 0;
    drmEventContext drm_ctx;
    struct pollfd pfd;
    struct drm_mode_create_dumb create_dumb;

    uint32_t read_hex_number_from_file(const std::string &path);
    std::string get_drm_device_path();
};
