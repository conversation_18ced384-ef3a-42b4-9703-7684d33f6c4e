podTemplate(
  podRetention: always(),
  containers: [
    containerTemplate(
      name: 'gpu-agent-ks4-builder',
      image: 'oci.xcube.com/gpu-agent-ks4-builder:v2.3.0',
      ttyEnabled: true,
      command: 'cat',
    )
  ]
) {
  node(POD_LABEL) {
    // JENKINS-30600
    stage('checkout') {
      // scm is a GitSCM instance
      checkout scm
    }

    stage('build gpu-agent-ks4') {
      container('gpu-agent-ks4-builder') {
        withEnv(['PATH+EXTRA=/opt/makeself:/opt/ninja-1.12.1-linux-aarch64']) {
          sh '''#!/bin/bash
          set -exo pipefail
          bash ./xbuild.sh
          '''
        }
      }
    }
  }
}
