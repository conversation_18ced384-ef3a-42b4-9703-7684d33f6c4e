[Unit]
Description=GPU Agent Display Forwarding Service
After=graphical-session.target

[Service]
Type=simple
User=root
Environment=DISPLAY=:0
ExecStartPre=/opt/xclient/bin/control_gpu_agent_service.sh
ExecStartPre=/opt/xclient/bin/check_display.sh
ExecStart=/bin/bash -c "/opt/xclient/bin/gpu-agent -f 15"
Restart=always
RestartSec=3
StartLimitBurst=3
StartLimitIntervalSec=30

[Install]
WantedBy=multi-user.target