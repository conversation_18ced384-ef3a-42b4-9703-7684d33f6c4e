#include "gpu-agent.hpp"

#define WIDTH 1920
#define HEIGHT 1080

#define THROW_ERROR(msg) do { \
    std::string error_msg = std::string(__func__) + ":" + std::to_string(__LINE__) + " - " + (msg); \
    throw GpuAgentException(error_msg); \
} while(0)

uint32_t pci_vendor_id_qxl = 0x1b36;
uint32_t pci_device_id_qxl = 0x0100;
uint32_t pci_vendor_id_virtio_gpu = 0x1af4;
uint32_t pci_device_id_virtio_gpu = 0x0010;

void GpuAgent::handle_page_flip(int fd, uint32_t sequence, uint32_t tv_sec, uint32_t tv_usec, void *user_data)
{
    (void)fd; (void)sequence; (void)tv_sec; (void)tv_usec; (void)user_data;
}

GpuAgent::GpuAgent(uint16_t width, uint16_t height, uint8_t fps) : width(width), height(height), fps(fps)
{
    display = nullptr;
    drm_fd = 0;
    res = nullptr;
    conn = nullptr;
    fb_data = nullptr;
    fb_id = 0;
    crtc_id = 0;
    frame_delay_us = (fps > 0) ? (1000000 / fps) : 50000;

    memset(&drm_ctx, 0, sizeof(drm_ctx));
    memset(&pfd, 0, sizeof(pfd));
    memset(&create_dumb, 0, sizeof(create_dumb));
}

GpuAgent::~GpuAgent()
{
    free_resource();
}

void GpuAgent::init_drm_event()
{
    memset(&drm_ctx, 0, sizeof(drm_ctx));
    drm_ctx.version = DRM_EVENT_CONTEXT_VERSION;
    drm_ctx.page_flip_handler = &GpuAgent::handle_page_flip;

    memset(&pfd, 0, sizeof(pfd));
    pfd.fd = this->drm_fd;
    pfd.events = POLLIN;
}

uint32_t GpuAgent::read_hex_number_from_file(const std::string &path)
{
    uint32_t res;
    std::ifstream vf(path);

    if (vf.fail()) {
        THROW_ERROR("Failed to open " + path + ": " + std::strerror(errno));
    }
    if (!(vf >> std::hex >> res)) {
        THROW_ERROR("Failed to read from " + path + ": " + std::strerror(errno));
    }
    return res;
}

std::string GpuAgent::get_drm_device_path()
{
    std::string drm_device_path = "";
    for (uint8_t card_id = 0; card_id < 5; ++card_id) {
        std::string card_name = "card" + std::to_string(card_id);
        std::string drm_path = "/dev/dri/" + card_name;
        struct stat stat_buf;
        if (stat(drm_path.c_str(), &stat_buf) != 0) {
            if (errno == ENOENT) {
                break;
            }
            THROW_ERROR("Error accessing DRM node for card : " + drm_path);
        }

        std::string sys_path = "/sys/class/drm/" + card_name;
        uint32_t vendor_id = read_hex_number_from_file(sys_path + "/device/vendor");
        uint32_t device_id = read_hex_number_from_file(sys_path + "/device/device");

        if ((vendor_id == pci_vendor_id_qxl && device_id == pci_device_id_qxl) ||
            (vendor_id == pci_vendor_id_virtio_gpu && device_id == pci_device_id_virtio_gpu)) {
            drm_device_path = drm_path;
            syslog(LOG_INFO, "Found Virtual GPU device %s", drm_device_path.c_str());
            break;
        }
    }
    return drm_device_path;
}

void GpuAgent::set_capture_resolution()
{
    display = XOpenDisplay(nullptr);
    if (display == nullptr) {
        THROW_ERROR("Cannot open display");
    }
    root_win = DefaultRootWindow(display);
    XWindowAttributes attrs;
    XGetWindowAttributes(display, root_win, &attrs);
    if (attrs.width < width) {
        width = attrs.width;
    }
    if (attrs.height < height) {
        height = attrs.height;
    }
}

void GpuAgent::free_resource()
{
    if (display) {
        XCloseDisplay(display);
        display = nullptr;
    }
    if (fb_id > 0 && drm_fd > 0) {
        drmModeRmFB(drm_fd, fb_id);
        fb_id = 0;
    }
    if (fb_data) {
        munmap(fb_data, create_dumb.size);
        fb_data = nullptr;
    }
    if (conn) {
        drmModeFreeConnector(conn);
        conn = nullptr;
    }
    if (res) {
        drmModeFreeResources(res);
        res = nullptr;
    }
    if (drm_fd > 0) {
        close(drm_fd);
        drm_fd = 0;
    }
}

void GpuAgent::set_drm()
{
    std::string drm_device_path = get_drm_device_path();
    drm_fd = open(drm_device_path.c_str(), O_RDWR | O_CLOEXEC);
    if (drm_fd < 0) {
        free_resource();
        THROW_ERROR("Cannot open DRM device " + drm_device_path);
    }
    // get DRM resources
    res = drmModeGetResources(drm_fd);
    if (!res) {
        free_resource();
        THROW_ERROR("Cannot get DRM resources");
    }
    // get DRM connector
    for (int i = 0; i < res->count_connectors; i++) {
        conn = drmModeGetConnector(drm_fd, res->connectors[i]);
        if (conn && conn->connection == DRM_MODE_CONNECTED && conn->count_modes > 0) {
            break;
        }
        drmModeFreeConnector(conn);
        conn = nullptr;
    }
    if (!conn) {
        free_resource();
        THROW_ERROR("Cannot get DRM connector");
    }

    // verify screen resolution is supported
    int mode_found = 0;
    int mode_index = 0;
    for (int i = 0; i < conn->count_modes; i++) {
        if (conn->modes[i].hdisplay == width && conn->modes[i].vdisplay == height) {
            mode_found = 1;
            mode_index = i;
            break;
        }
    }
    if (!mode_found) {
        syslog(LOG_WARNING, "VirtIO-GPU/QXL does not support resolution %dx%d, try 1280x800", width, height);
        width = 1280;
        height = 800;
        for (int i = 0; i < conn->count_modes; i++) {
            if (conn->modes[i].hdisplay == width && conn->modes[i].vdisplay == height) {
                mode_found = 1;
                mode_index = i;
                break;
            }
        }
        if (!mode_found) {
            free_resource();
            THROW_ERROR("VirtIO-GPU/QXL does not support resolution 1280x800");
        }
    }
    // init dumb buffer
    create_dumb.width = width;
    create_dumb.height = height;
    create_dumb.bpp = 32;

    if (drmIoctl(drm_fd, DRM_IOCTL_MODE_CREATE_DUMB, &create_dumb) < 0) {
        free_resource();
        THROW_ERROR("Cannot create dumb buffer");
    }

    // map dumb buffer
    struct drm_mode_map_dumb map;
    memset(&map, 0, sizeof(map));
    map.handle = create_dumb.handle;
    if (drmIoctl(drm_fd, DRM_IOCTL_MODE_MAP_DUMB, &map) < 0) {
        free_resource();
        THROW_ERROR("Cannot map dumb buffer");
    }
    fb_data = mmap(NULL, create_dumb.size, PROT_READ | PROT_WRITE, MAP_SHARED, drm_fd, map.offset);
    if (fb_data == MAP_FAILED) {
        free_resource();
        THROW_ERROR("Cannot mmap dumb buffer");
    }

    // create framebuffer
    if (drmModeAddFB(drm_fd, width, height, 24, create_dumb.bpp, create_dumb.pitch, create_dumb.handle, &fb_id) < 0) {
        free_resource();
        THROW_ERROR("Cannot create framebuffer");
    }
    // set CRTC
    crtc_id = res->crtcs[0];
    syslog(LOG_INFO, "set CRTC: crtc_id=%u, fb_id=%u, connector_id=%u, %dx%d, mode_index=%d",
           crtc_id, fb_id, conn->connector_id, width, height, mode_index);
    if (drmModeSetCrtc(drm_fd, crtc_id, fb_id, 0, 0, &conn->connector_id, 1, &conn->modes[mode_index]) < 0) {
        free_resource();
        THROW_ERROR("Cannot set CRTC");
    }
}

void GpuAgent::start_capture()
{
    while (1) {
        XImage *image = XGetImage(display, root_win, 0, 0, width, height, AllPlanes, ZPixmap);
        if (!image) {
            syslog(LOG_ERR, "Cannot capture X image");
            break;
        }
        size_t image_size = image->bytes_per_line * image->height;
        size_t copy_size = (image_size < create_dumb.size) ? image_size : create_dumb.size;
        memcpy(fb_data, image->data, copy_size);
        if (drmModePageFlip(drm_fd, crtc_id, fb_id, DRM_MODE_PAGE_FLIP_EVENT, NULL) < 0) {
            syslog(LOG_ERR, "Cannot page flip");
            break;
        } else {
            poll(&pfd, 1, -1);
            if (drmHandleEvent(drm_fd, &drm_ctx) < 0) {
                syslog(LOG_ERR, "drmHandleEvent failed: %s", strerror(errno));
            }
        }
        XDestroyImage(image);
        usleep(frame_delay_us);
    }
}

static void handle_interrupt(int intr)
{
    syslog(LOG_INFO, "Got signal %d, setting exit flag", intr);
    exit(0);
}

static void register_interrupts(void)
{
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = handle_interrupt;
    if ((sigaction(SIGINT, &sa, NULL) != 0) ||
        (sigaction(SIGTERM, &sa, NULL) != 0)) {
        syslog(LOG_WARNING, "failed to register signal handler");
    }
}

static void print_usage(const char* program_name)
{
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -f, --fps FPS        Set capture frame rate (default: 20)\n"
              << "  -h, --help           Show this help message\n"
              << "\nExample:\n"
              << "  " << program_name << " --fps 60\n"
              << "  " << program_name << " -f 30\n"
              << std::endl;
}

static uint16_t parse_fps_argument(int argc, char* argv[])
{
    uint8_t fps = 20;
    int opt;
    int option_index = 0;

    static struct option long_options[] = {
        {"fps",  required_argument, 0, 'f'},
        {"help", no_argument,       0, 'h'},
        {0, 0, 0, 0}
    };

    while ((opt = getopt_long(argc, argv, "f:h", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'f':
                {
                    int temp_fps = std::atoi(optarg);
                    if (temp_fps <= 0 || temp_fps > 100) {
                        std::cerr << "Error: FPS must be between 1 and 100" << std::endl;
                        print_usage(argv[0]);
                        THROW_ERROR("FPS must be between 1 and 100");
                    }
                    fps = static_cast<uint16_t>(temp_fps);
                    syslog(LOG_INFO, "Set FPS to %d", fps);
                }
                break;
            case 'h':
                print_usage(argv[0]);
                THROW_ERROR("Help requested");
                break;
            case '?':
                print_usage(argv[0]);
                THROW_ERROR("Invalid argument");
                break;
            default:
                print_usage(argv[0]);
                THROW_ERROR("Unknown argument");
        }
    }
    return fps;
}

int main(int argc, char* argv[])
{
    try {
        register_interrupts();
        uint8_t fps = parse_fps_argument(argc, argv);
        GpuAgent gpu_agent(WIDTH, HEIGHT, fps);
        gpu_agent.set_capture_resolution();
        gpu_agent.set_drm();
        gpu_agent.init_drm_event();
        gpu_agent.start_capture();
        return 0;

    } catch (const GpuAgentException& e) {
        if (std::string(e.what()).find("Help requested") != std::string::npos) {
            return 0;
        }
        std::cerr << "Error: " << e.what() << std::endl;
        syslog(LOG_ERR, "%s", e.what());
        return 1;

    } catch (const std::exception& e) {
        std::cerr << "Unexpected error: " << e.what() << std::endl;
        syslog(LOG_ERR, "Unexpected error: %s", e.what());
        return 1;
    }
}
