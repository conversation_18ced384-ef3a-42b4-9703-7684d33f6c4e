project('gpu-agent', 'cpp',
  version : '1.0.0',
  license : 'LGPLv2.1',
  default_options : [
    'cpp_std=c++11',
    'warning_level=2',
    'buildtype=debugoptimized',
    'b_ndebug=if-release',
  ]
)

# Project information
project_description = 'High-performance screen capture and display forwarding tool for Linux virtual machines'

# Compiler setup
cpp = meson.get_compiler('cpp')

# Required dependencies
libdrm_dep = dependency('libdrm', version : '>=2.4.0', required : true)
x11_dep = dependency('x11', required : true)

# Check for required headers
if not cpp.has_header('xf86drm.h')
  error('xf86drm.h header not found. Please install libdrm development package.')
endif

if not cpp.has_header('xf86drmMode.h')
  error('xf86drmMode.h header not found. Please install libdrm development package.')
endif

if not cpp.has_header('X11/Xlib.h')
  error('X11/Xlib.h header not found. Please install libX11 development package.')
endif

add_project_arguments([
  '-Werror=unused-function',
  '-Werror=unused-variable',
  '-Werror=unused-but-set-variable',
], language : 'cpp')

# Source files
gpu_agent_sources = [
  'gpu-agent.cpp'
]

# Header files
gpu_agent_headers = [
  'gpu-agent.hpp'
]

# Collect all dependencies
gpu_agent_deps = [
  libdrm_dep,
  x11_dep,
]

# Main executable
gpu_agent_exe = executable('gpu-agent',
  gpu_agent_sources,
  dependencies : gpu_agent_deps,
  install : true
)

# Configuration data for pkg-config file
conf_data = configuration_data()
conf_data.set('VERSION', meson.project_version())
conf_data.set('PREFIX', get_option('prefix'))
conf_data.set('LIBDIR', join_paths(get_option('prefix'), get_option('libdir')))
conf_data.set('INCLUDEDIR', join_paths(get_option('prefix'), get_option('includedir')))

# Summary
summary({
  'Project' : meson.project_name(),
  'Version' : meson.project_version(),
  'Build type' : get_option('buildtype'),
  'C++ standard' : get_option('cpp_std'),
  'Install prefix' : get_option('prefix'),
}, section : 'Configuration')

summary({
  'libdrm' : libdrm_dep.found() ? libdrm_dep.version() : 'not found',
  'X11' : x11_dep.found() ? 'found' : 'not found',
}, section : 'Dependencies')
