#!/bin/bash -e

JMGPU_VENDOR_ID="0731"
jmgpu_dev_path=""

detect_gpu_cards() {
    for card_num in {0..5}; do
        drm_card_path="/sys/class/drm/card$card_num"
        vendor_file="$drm_card_path/device/vendor"
        vendor_id=$(cat "$vendor_file" 2>/dev/null | sed 's/^0x//')
        if [ "$vendor_id" == "$JMGPU_VENDOR_ID" ]; then
            jmgpu_dev_path="/dev/dri/card$card_num"
            break
        fi
    done
}

detect_gpu_cards
if [ -n "$jmgpu_dev_path" ]; then
    echo "jmgpu card found, gpu-agent service can run..."
else
    systemctl stop gpu-agent.service
    echo "jmgpu card not found, stop gpu-agent service..."
    exit 1
fi
