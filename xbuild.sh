#!/bin/bash

set -e

verstr=$(git describe --long --match 'v*' 2> /dev/null | sed 's/^v//')

if [ -z "$verstr" ];then
    echo >&2 "error - no valid tag found" && exit 1
fi

dirty=$(test -n "$(git status --porcelain --untracked-files=no)" && echo "+CHANGES" || true)

if expr index $verstr '-' > /dev/null; then
    ver=$(echo $verstr | cut -d - -f 1-1)
    rel=$(echo $verstr | cut -d - -f 2- | sed 's/-/./g')$dirty
else
    ver=$verstr
    rel=0$dirty
fi

VR=$ver-$rel

rm -rf xwork
mkdir -p xwork

_topdir=$(realpath xwork)

cd xwork

mkdir -p xbuildroot

meson setup --buildtype=release --prefix=/usr --strip xbuild ../
meson compile -C xbuild
DESTDIR=${_topdir}/xbuildroot meson install -C xbuild

mkdir -p makeself.d

install -m 0644 -D xbuildroot/usr/bin/gpu-agent makeself.d/gpu-agent

cp ../xsetup/check_display.sh makeself.d
cp ../xsetup/control_gpu_agent_service.sh makeself.d
cp ../xsetup/gpu-agent.service makeself.d
cp ../xsetup/xsetup.sh makeself.d

chmod a+x makeself.d/xsetup.sh

makeself --keep-umask makeself.d  gpu-agent-$VR.run "installer gpu-agent" ./xsetup.sh

echo ""
echo "*************************************"
echo "*************************************"
echo "make gpu-agent run package finish"
echo "*************************************"
echo "*************************************"
