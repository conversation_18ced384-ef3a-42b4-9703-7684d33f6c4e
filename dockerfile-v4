# syntax=docker/dockerfile:1
FROM oci.xcube.com/kylin-server-v4:latest

ARG TOOLS_URL=http://nas.xcube.com/dev-tools

RUN <<EOF

apt-get update

apt-get install -y wget tar vim git gcc
apt-get install -y python3-pip make pkg-config
apt-get install -y libx11-dev libxfixes-dev libdrm-dev

apt-get autoremove

pip3 install meson==0.55.0 -i https://pypi.tuna.tsinghua.edu.cn/simple

wget -P /opt $TOOLS_URL/ninja-1.12.1-linux-aarch64.tar.gz
tar -zxvf /opt/ninja-1.12.1-linux-aarch64.tar.gz -C /opt
echo 'export PATH=/opt/ninja-1.12.1-linux-aarch64:$PATH' >>/root/.bashrc

wget -P /opt $TOOLS_URL/makeself-2.5.0.run
sh /opt/makeself-2.5.0.run --target /opt/makeself
ln -sfr /opt/makeself/makeself.sh /opt/makeself/makeself
rm -f /opt/makeself-2.5.0.run

echo 'export PATH=/opt/makeself:$PATH' >>/root/.bashrc
echo 'alias vi=vim' >>/root/.bashrc

cat >>/root/.gitconfig <<EOFx

[safe]
    directory = *
EOFx
EOF

WORKDIR /root
CMD ["/bin/bash"]
