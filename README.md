# GPU Agent

一个用于Linux虚拟机的高性能屏幕捕获和显示转发工具。GPU Agent从物理显卡捕获显示内容，并实时转发到虚拟GPU设备(VirtIO-GPU或QXL).

## 功能特性

- **实时屏幕捕获**: 以可配置的帧率捕获X11桌面内容
- **虚拟GPU支持**: 兼容VirtIO-GPU和QXL虚拟图形设备
- **DRM集成**: 直接渲染管理器集成，实现高效GPU操作
- **自动设备检测**: 自动检测和配置虚拟GPU设备
- **信号处理**: 在SIGINT/SIGTERM信号时关闭
- **错误处理**: 全面的错误处理和syslog集成
- **可配置FPS**: 可调节帧率1-100 FPS (默认:20 FPS)

## 使用场景

- **虚拟机显示转发**: 将物理GPU显示转发到虚拟机

## 系统要求

### 系统需求
- Linux操作系统
- X11显示服务器
- 支持DRM的图形硬件
- 虚拟GPU设备(VirtIO-GPU或QXL)

### 依赖项
- **libdrm**: 直接渲染管理器库
- **libX11**: X11客户端库

### 构建依赖项
- **g++**: 支持C++11的C++编译器
- **meson/ninja**: 构建系统
- **pkg-config**: 包配置工具
- **libX11-dev**: X11开发头文件
- **libdrm-dev**: drm开发头文件

## 安装

### 安装依赖项 (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install gcc-c++ libdrm-dev libx11-dev pkg-config
```

### 安装依赖项 (CentOS/RHEL)
```bash
# CentOS/RHEL
sudo yum install gcc-c++ libdrm-devel libX11-devel pkgconfig
```

### 从源码构建
```bash
git clone http://git.xcube.com/spice/gpu-agent.git
cd gpu-agent
g++ -o gpu-agent gpu-agent.cpp gpu-agent.hpp $(pkg-config --cflags --libs libdrm) -lX11
```

## 使用方法

### 基本用法
```bash
# 使用默认设置运行(20 FPS)
./gpu-agent
```

### 命令行选项
- `-f, --fps FPS`: 设置捕获帧率 (1-100，默认: 15)
- `-h, --help`: 显示帮助信息

### 示例
```bash
# 高帧率模式 (60 FPS)
./gpu-agent --fps 60

# 中帧率模式 (30 FPS)
./gpu-agent -fps 30

# 低帧率模式 (10 FPS)
./gpu-agent --fps 10
```

## 配置

### 支持的分辨率
- **默认**: 1920x1080
- **备用**: 1280x800 (如果默认分辨率不支持)
- **自动检测**: 自动检测并使用可用的显示模式

### 支持的虚拟GPU
- **VirtIO-GPU**: PCI ID 1af4:0010
- **QXL**: PCI ID 1b36:0100

## 架构

### 核心组件
1. **X11捕获**: 使用XGetImage捕获桌面内容
2. **DRM接口**: 通过直接渲染管理器管理虚拟GPU
3. **帧缓冲**: 创建和管理GPU帧缓冲
4. **事件处理**: 处理DRM页面翻转事件

### 数据流
```
X11桌面 → XGetImage → 帧缓冲 → DRM页面翻转 → 虚拟GPU → 虚拟机显示
```

## 故障排除

### 常见问题

**未找到虚拟GPU**
- 确保虚拟机已配置VirtIO-GPU或QXL设备
- 检查`/dev/dri/`目录是否存在且可访问
- 验证虚拟GPU驱动程序已加载

**显示未更新**

- 检查X11 DISPLAY环境变量
- 确保X11服务器正在运行
- 验证用户有权访问X11显示

**无权访问虚拟显卡**

- 检查Xorg 是否运行在/dev/dri/cardx上

### 调试信息
```bash
# 检查系统日志
journalctl -f | grep gpu-agent

# 检查DRM设备
ls -la /dev/dri/

# 检查虚拟GPU信息
lspci | grep -i vga
```

## 性能

### 优化建议
- **降低FPS**: 使用较低的帧率减少CPU使用率
- **分辨率**: 使用较低的分辨率获得更好的性能
- **虚拟机资源**: 为虚拟机分配足够的CPU和内存

### 基准测试
- **1920x1080@30fps**: 约50%CPU使用率
- **1290x1080@15fps**: 约30%CPU使用率
- **内存使用**: 约50MB

## 服务安装

### 作为Systemd服务运行
创建服务文件`/etc/systemd/system/gpu-agent.service`
```ini
[Unit]
Description=GPU Agent Display Forwarding Service
After=graphical-session.target

[Service]
Type=simple
User=root
Environment=DISPLAY=:0
ExecStart=/opt/xclient/bin/gpu-agent --fps 30
Restart=always
RestartSec=5
StartLimitBurst=10
StartLimitIntervalSec=60

[Install]
WantedBy=multi-user.target
```

启用并启动服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable gpu-agent
sudo systemctl start gpu-agent
```

## 支持

如有问题和疑问:
- 检查系统日志中的错误信息
- 查看故障排除部分
- 确保所有依赖项都已正确安装