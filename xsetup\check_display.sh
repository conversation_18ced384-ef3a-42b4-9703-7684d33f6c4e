#!/bin/bash
# 循环检测显示0 是否就绪，可以获取则退出

Active_Display=""

detect_active_display() {
    for sessionid in $(loginctl list-sessions --no-legend | awk '{ print $1}')
    do
        state=`loginctl show-session -p State  $sessionid`
        if test $state = "State=active"; then
            i_type=`loginctl show-session -p Type  $sessionid`
            if test $i_type = "Type=x11"; then
                i_display=`loginctl show-session -p Display  $sessionid`
                Active_Display=${i_display#*=}
                break
            fi
         fi
    done
}

while true; do
    detect_active_display
    if [ "$Active_Display" != "" ]; then
        echo "get display successed, DISPLAY=${Active_Display}"
        break
    else
        echo "get display 0 failed, continue wait..."
        sleep 1  
    fi
done

xauth add `xauth -f /var/run/lightdm/root/${Active_Display} list`

